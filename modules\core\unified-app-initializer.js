/**
 * @file 统一应用初始化器
 * <AUTHOR> Team
 * @description 
 * 整合js/app-initializer.js和src/app/smart-office-app.js的功能
 * 提供统一的应用初始化和管理接口
 * 
 * 整合特性：
 * - js/版本的模块化架构和现代化接口
 * - src/版本的完整功能和工作流引擎
 * - 统一的错误处理和性能监控
 * - 兼容file://和HTTP双模式运行
 */

import { getLogger } from '../../js/utils/logger.js';
import { ConfigManager } from '../../js/config/config-manager.js';
import { EventBus } from '../../js/utils/event-bus.js';

// #region 统一应用初始化器类
/**
 * @class UnifiedAppInitializer - 统一应用初始化器
 * @description 整合双重架构的应用初始化器，提供完整的应用管理功能
 */
export class UnifiedAppInitializer {
    /**
     * 构造函数
     * @param {Object} config - 应用配置
     * @param {HTMLElement} container - 容器元素
     */
    constructor(config = {}, container = null) {
        // 基础属性
        this.name = 'SmartOffice';
        this.version = '2.0.0-unified';
        this.description = 'SmartOffice 2.0 统一架构版本';
        
        // 核心依赖
        this.logger = getLogger();
        this.configManager = new ConfigManager();
        this.eventBus = new EventBus();
        
        // 应用状态
        this.isInitialized = false;
        this.isReady = false;
        this.container = container;
        this.startTime = performance.now();
        
        // 模块管理
        this.modules = new Map();
        this.managers = {
            config: null,           // 配置管理器
            state: null,            // 状态管理器
            workflow: null,         // 工作流引擎
            components: null,       // UI组件管理器
            templates: null,        // 模板管理器
            renderers: null,        // 渲染器管理器
            exporters: null,        // 导出器管理器
            nlp: null,              // NLP处理器
            resources: null         // 资源管理器
        };
        
        // 应用配置
        this.config = {
            // 基础配置
            autoStart: config.autoStart !== false,
            enableResourceFallback: config.enableResourceFallback !== false,
            enablePerformanceMonitoring: config.enablePerformanceMonitoring !== false,
            autoInit: config.autoInit !== false,
            enablePlugins: config.enablePlugins !== false,
            enableWorkflow: config.enableWorkflow !== false,
            
            // 语言和主题
            defaultLanguage: config.defaultLanguage || 'zh-CN',
            defaultTheme: config.defaultTheme || 'default',
            defaultDocumentType: config.defaultDocumentType || 'receipt',
            
            // 工作流配置
            workflow: {
                autoSave: true,
                autoPreview: true,
                smartTemplateSelection: true,
                batchProcessing: false,
                ...config.workflow
            },
            
            // UI配置
            ui: {
                showToolbar: true,
                showSidebar: true,
                showStatusBar: true,
                enableKeyboardShortcuts: true,
                enableAnimations: true,
                compactMode: false,
                ...config.ui
            },
            
            // 导出配置
            export: {
                defaultFormat: 'pdf',
                quality: 'high',
                autoDownload: true,
                showProgress: true,
                enableBatch: true,
                maxConcurrent: 3,
                ...config.export
            },
            
            // 性能配置
            performance: {
                enableCache: true,
                cacheSize: 100,
                enableLazyLoading: true,
                enableOptimization: true,
                ...config.performance
            },
            
            // 合并其他配置
            ...config
        };
        
        // 应用统计
        this.stats = {
            documentsGenerated: 0,
            templatesUsed: new Set(),
            exportFormats: new Map(),
            averageProcessingTime: 0,
            totalProcessingTime: 0,
            startTime: Date.now(),
            initializationSteps: 0,
            errors: 0,
            warnings: 0
        };
        
        // 初始化步骤
        this.initializationSteps = [];
        this.currentStep = 0;
        this.initErrors = [];
        this.warnings = [];
        
        this.logger.info('UnifiedAppInitializer', 'constructor', '🚀 统一应用初始化器创建');
        
        // 自动初始化
        if (this.config.autoInit) {
            setTimeout(() => {
                this.initialize().catch(error => {
                    this.logger.error('UnifiedAppInitializer', 'constructor', '自动初始化失败', { error });
                    this.eventBus.emit('app:error', error);
                });
            }, 0);
        }
    }
    
    /**
     * 初始化应用
     * @returns {Promise<void>}
     */
    async initialize() {
        if (this.isInitialized) {
            this.logger.warn('UnifiedAppInitializer', 'initialize', '应用已初始化，跳过重复初始化');
            return;
        }
        
        try {
            this.logger.info('UnifiedAppInitializer', 'initialize', '🔄 开始初始化统一应用');
            this.logger.startPerformanceMark('unified_app_initialization', 'UnifiedAppInitializer', 'initialize');
            
            // 定义初始化步骤
            this._defineInitializationSteps();
            
            // 执行初始化步骤
            await this._executeInitializationSteps();
            
            // 标记为已初始化
            this.isInitialized = true;
            this.isReady = true;
            
            const initDuration = this.logger.endPerformanceMark('unified_app_initialization', 'UnifiedAppInitializer', 'initialize');
            
            // 设置全局应用引用
            this._setupGlobalReferences();
            
            // 触发初始化完成事件
            this.eventBus.emit('app:initialized', {
                duration: initDuration,
                steps: this.initializationSteps.length,
                errors: this.initErrors.length,
                warnings: this.warnings.length,
                timestamp: new Date()
            });
            
            this.logger.info('UnifiedAppInitializer', 'initialize', '✅ 统一应用初始化完成', {
                duration: initDuration,
                modulesCount: this.modules.size,
                managersCount: Object.values(this.managers).filter(m => m !== null).length
            });
            
        } catch (error) {
            this.logger.error('UnifiedAppInitializer', 'initialize', '❌ 应用初始化失败', { error });
            this.eventBus.emit('app:error', error);
            throw error;
        }
    }
    
    /**
     * 定义初始化步骤
     * @private
     */
    _defineInitializationSteps() {
        this.initializationSteps = [
            {
                name: 'dom-ready',
                description: 'DOM就绪检查',
                handler: this._waitForDOM.bind(this),
                required: true
            },
            {
                name: 'environment-detection',
                description: '环境检测',
                handler: this._detectEnvironment.bind(this),
                required: true
            },
            {
                name: 'config-initialization',
                description: '配置初始化',
                handler: this._initializeConfig.bind(this),
                required: true
            },
            {
                name: 'resource-manager',
                description: '资源管理器初始化',
                handler: this._initializeResourceManager.bind(this),
                required: true
            },
            {
                name: 'core-modules',
                description: '核心模块初始化',
                handler: this._initializeCoreModules.bind(this),
                required: true
            },
            {
                name: 'managers',
                description: '管理器初始化',
                handler: this._initializeManagers.bind(this),
                required: true
            },
            {
                name: 'ui-components',
                description: 'UI组件初始化',
                handler: this._initializeUIComponents.bind(this),
                required: true
            },
            {
                name: 'workflow-engine',
                description: '工作流引擎初始化',
                handler: this._initializeWorkflowEngine.bind(this),
                required: false
            },
            {
                name: 'global-handlers',
                description: '全局处理器设置',
                handler: this._setupGlobalHandlers.bind(this),
                required: true
            }
        ];
    }
    
    /**
     * 执行初始化步骤
     * @private
     * @returns {Promise<Object>}
     */
    async _executeInitializationSteps() {
        const results = {};
        
        for (let i = 0; i < this.initializationSteps.length; i++) {
            const step = this.initializationSteps[i];
            this.currentStep = i + 1;
            
            try {
                this.logger.debug('UnifiedAppInitializer', '_executeInitializationSteps', 
                    `执行步骤 ${this.currentStep}/${this.initializationSteps.length}: ${step.description}`);
                
                // 触发步骤开始事件
                this.eventBus.emit('app:init-step', {
                    step: step.name,
                    description: step.description,
                    current: this.currentStep,
                    total: this.initializationSteps.length,
                    timestamp: new Date()
                });
                
                // 执行步骤
                const stepResult = await step.handler();
                results[step.name] = stepResult;
                
                this.stats.initializationSteps++;
                
            } catch (error) {
                this.logger.error('UnifiedAppInitializer', '_executeInitializationSteps', 
                    `步骤失败: ${step.description}`, { error });
                
                this.initErrors.push({
                    step: step.name,
                    error: error.message,
                    timestamp: new Date()
                });
                
                this.stats.errors++;
                
                if (step.required) {
                    throw new Error(`必需步骤失败: ${step.description} - ${error.message}`);
                } else {
                    this.warnings.push({
                        step: step.name,
                        warning: `可选步骤失败: ${error.message}`,
                        timestamp: new Date()
                    });
                    this.stats.warnings++;
                }
            }
        }
        
        return results;
    }
    
    /**
     * 等待DOM就绪
     * @private
     * @returns {Promise<void>}
     */
    async _waitForDOM() {
        if (document.readyState === 'complete') {
            return;
        }
        
        return new Promise((resolve) => {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', resolve, { once: true });
            } else {
                resolve();
            }
        });
    }
    
    /**
     * 检测环境
     * @private
     * @returns {Object}
     */
    _detectEnvironment() {
        const environment = {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            protocol: window.location.protocol,
            isFileProtocol: window.location.protocol === 'file:',
            isHttpProtocol: window.location.protocol.startsWith('http'),
            timestamp: new Date()
        };
        
        this.logger.debug('UnifiedAppInitializer', '_detectEnvironment', '环境检测完成', environment);
        return environment;
    }
    
    /**
     * 初始化配置
     * @private
     * @returns {Promise<void>}
     */
    async _initializeConfig() {
        // 初始化配置管理器
        await this.configManager.initialize(this.config);
        this.managers.config = this.configManager;

        this.logger.debug('UnifiedAppInitializer', '_initializeConfig', '配置管理器初始化完成');
    }

    /**
     * 初始化资源管理器
     * @private
     * @returns {Promise<void>}
     */
    async _initializeResourceManager() {
        try {
            const { ResourceManager } = await import('../../js/utils/resource-manager.js');
            const resourceManager = new ResourceManager(this.configManager, this.eventBus);
            await resourceManager.initialize();

            this.modules.set('resourceManager', resourceManager);
            this.managers.resources = resourceManager;

            this.logger.debug('UnifiedAppInitializer', '_initializeResourceManager', '资源管理器初始化完成');
        } catch (error) {
            this.logger.error('UnifiedAppInitializer', '_initializeResourceManager', '资源管理器初始化失败', { error });
            throw error;
        }
    }

    /**
     * 初始化核心模块
     * @private
     * @returns {Promise<void>}
     */
    async _initializeCoreModules() {
        try {
            // 初始化通知管理器
            const { NotificationManager } = await import('../../js/utils/notification-manager.js');
            const notificationManager = new NotificationManager(this.configManager, this.eventBus);
            this.modules.set('notificationManager', notificationManager);

            // 初始化DOM辅助工具
            const { DOMHelpers } = await import('../../js/utils/dom-helpers.js');
            const domHelpers = new DOMHelpers();
            this.modules.set('domHelpers', domHelpers);

            this.logger.debug('UnifiedAppInitializer', '_initializeCoreModules', '核心模块初始化完成');
        } catch (error) {
            this.logger.error('UnifiedAppInitializer', '_initializeCoreModules', '核心模块初始化失败', { error });
            throw error;
        }
    }

    /**
     * 初始化管理器
     * @private
     * @returns {Promise<void>}
     */
    async _initializeManagers() {
        try {
            // 初始化状态管理器
            const { GlobalStateManager } = await import('../../src/state/global-state-manager.js');
            const stateManager = new GlobalStateManager();
            this.managers.state = stateManager;

            // 初始化渲染器管理器
            const { UnifiedRenderer } = await import('../../src/renderers/unified-renderer.js');
            const rendererManager = new UnifiedRenderer();
            this.managers.renderers = rendererManager;

            // 初始化导出器管理器
            const { BaseExporter } = await import('../../src/exporters/base-exporter.js');
            const exporterManager = new BaseExporter();
            this.managers.exporters = exporterManager;

            this.logger.debug('UnifiedAppInitializer', '_initializeManagers', '管理器初始化完成');
        } catch (error) {
            this.logger.error('UnifiedAppInitializer', '_initializeManagers', '管理器初始化失败', { error });
            throw error;
        }
    }

    /**
     * 初始化UI组件
     * @private
     * @returns {Promise<void>}
     */
    async _initializeUIComponents() {
        try {
            // 初始化文档编辑器
            const { DocumentEditor } = await import('../../js/ui/document-editor.js');
            const documentEditor = new DocumentEditor(this.configManager, this.eventBus);
            this.modules.set('documentEditor', documentEditor);

            this.logger.debug('UnifiedAppInitializer', '_initializeUIComponents', 'UI组件初始化完成');
        } catch (error) {
            this.logger.error('UnifiedAppInitializer', '_initializeUIComponents', 'UI组件初始化失败', { error });
            throw error;
        }
    }

    /**
     * 初始化工作流引擎
     * @private
     * @returns {Promise<void>}
     */
    async _initializeWorkflowEngine() {
        if (!this.config.enableWorkflow) {
            this.logger.debug('UnifiedAppInitializer', '_initializeWorkflowEngine', '工作流引擎已禁用');
            return;
        }

        try {
            const { WorkflowEngine } = await import('../../src/workflow/workflow-engine.js');
            const workflowEngine = new WorkflowEngine();
            this.managers.workflow = workflowEngine;

            this.logger.debug('UnifiedAppInitializer', '_initializeWorkflowEngine', '工作流引擎初始化完成');
        } catch (error) {
            this.logger.warn('UnifiedAppInitializer', '_initializeWorkflowEngine', '工作流引擎初始化失败', { error });
            // 工作流引擎是可选的，失败不影响应用启动
        }
    }

    /**
     * 设置全局处理器
     * @private
     * @returns {void}
     */
    _setupGlobalHandlers() {
        // 设置全局错误处理
        window.addEventListener('error', (event) => {
            this.logger.error('UnifiedAppInitializer', 'globalErrorHandler', '全局错误', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });

            this.eventBus.emit('app:global-error', {
                type: 'javascript',
                message: event.message,
                source: event.filename,
                line: event.lineno,
                column: event.colno,
                timestamp: new Date()
            });
        });

        // 设置未处理的Promise拒绝处理
        window.addEventListener('unhandledrejection', (event) => {
            this.logger.error('UnifiedAppInitializer', 'unhandledRejectionHandler', '未处理的Promise拒绝', {
                reason: event.reason
            });

            this.eventBus.emit('app:unhandled-rejection', {
                reason: event.reason,
                timestamp: new Date()
            });
        });

        this.logger.debug('UnifiedAppInitializer', '_setupGlobalHandlers', '全局处理器设置完成');
    }

    /**
     * 设置全局引用
     * @private
     */
    _setupGlobalReferences() {
        // 设置全局应用引用
        window.smartOfficeApp = this.createAppInterface();

        // 设置SmartOffice命名空间
        window.SmartOffice = {
            App: UnifiedAppInitializer,
            app: this,
            version: this.version,
            getApp: () => this,
            getAppInfo: () => this.getAppInfo()
        };

        this.logger.debug('UnifiedAppInitializer', '_setupGlobalReferences', '全局引用设置完成');
    }

    /**
     * 创建应用接口
     * @private
     * @returns {Object}
     */
    createAppInterface() {
        return {
            version: this.version,
            name: this.name,
            mode: 'unified',
            isInitialized: () => this.isInitialized,
            isReady: () => this.isReady,

            // 获取模块和管理器
            getModule: (moduleName) => this.getModule(moduleName),
            getManager: (managerName) => this.getManager(managerName),
            getModules: () => Array.from(this.modules.keys()),
            getManagers: () => Object.keys(this.managers).filter(key => this.managers[key] !== null),

            // 获取应用信息
            getAppInfo: () => this.getAppInfo(),

            // 事件系统
            on: (event, handler) => this.eventBus.on(event, handler),
            off: (event, handler) => this.eventBus.off(event, handler),
            emit: (event, data) => this.eventBus.emit(event, data),

            // 配置访问
            getConfig: (key, defaultValue) => this.configManager.get(key, defaultValue),
            setConfig: (key, value) => this.configManager.set(key, value)
        };
    }

    /**
     * 获取应用信息
     * @returns {Object}
     */
    getAppInfo() {
        return {
            name: this.name,
            version: this.version,
            description: this.description,
            mode: 'unified',
            isInitialized: this.isInitialized,
            isReady: this.isReady,
            modulesCount: this.modules.size,
            managersCount: Object.values(this.managers).filter(m => m !== null).length,
            uptime: performance.now() - this.startTime,
            stats: { ...this.stats },
            config: { ...this.config },
            container: !!this.container,
            features: {
                nlp: 'gemini',
                export: 'full',
                offline: this.config.enableResourceFallback,
                modules: 'es6',
                unified: true,
                workflow: this.config.enableWorkflow
            }
        };
    }
    
    /**
     * 获取模块
     * @param {string} moduleName - 模块名称
     * @returns {*}
     */
    getModule(moduleName) {
        return this.modules.get(moduleName);
    }
    
    /**
     * 获取管理器
     * @param {string} managerName - 管理器名称
     * @returns {*}
     */
    getManager(managerName) {
        return this.managers[managerName];
    }
    
    /**
     * 检查应用是否就绪
     * @returns {boolean}
     */
    isReady() {
        return this.isInitialized && this.isReady;
    }

    /**
     * 销毁应用
     * @returns {Promise<void>}
     */
    async destroy() {
        this.logger.info('UnifiedAppInitializer', 'destroy', '开始销毁应用');

        try {
            // 触发销毁事件
            this.eventBus.emit('app:destroying');

            // 销毁所有模块
            for (const [name, module] of this.modules) {
                if (module && typeof module.destroy === 'function') {
                    try {
                        await module.destroy();
                        this.logger.debug('UnifiedAppInitializer', 'destroy', `模块 ${name} 销毁完成`);
                    } catch (error) {
                        this.logger.error('UnifiedAppInitializer', 'destroy', `模块 ${name} 销毁失败`, { error });
                    }
                }
            }

            // 销毁所有管理器
            for (const [name, manager] of Object.entries(this.managers)) {
                if (manager && typeof manager.destroy === 'function') {
                    try {
                        await manager.destroy();
                        this.logger.debug('UnifiedAppInitializer', 'destroy', `管理器 ${name} 销毁完成`);
                    } catch (error) {
                        this.logger.error('UnifiedAppInitializer', 'destroy', `管理器 ${name} 销毁失败`, { error });
                    }
                }
            }

            // 清理全局引用
            if (window.smartOfficeApp) {
                delete window.smartOfficeApp;
            }
            if (window.SmartOffice) {
                delete window.SmartOffice;
            }

            // 重置状态
            this.isInitialized = false;
            this.isReady = false;
            this.modules.clear();
            Object.keys(this.managers).forEach(key => {
                this.managers[key] = null;
            });

            // 触发销毁完成事件
            this.eventBus.emit('app:destroyed');

            this.logger.info('UnifiedAppInitializer', 'destroy', '应用销毁完成');

        } catch (error) {
            this.logger.error('UnifiedAppInitializer', 'destroy', '应用销毁失败', { error });
            throw error;
        }
    }
}
// #endregion

// #region 工厂函数和全局实例管理
let globalUnifiedApp = null;

/**
 * 创建统一应用实例
 * @param {Object} config - 应用配置
 * @param {HTMLElement} container - 容器元素
 * @returns {UnifiedAppInitializer}
 */
export function createUnifiedApp(config = {}, container = null) {
    return new UnifiedAppInitializer(config, container);
}

/**
 * 获取全局统一应用实例
 * @param {Object} config - 应用配置
 * @param {HTMLElement} container - 容器元素
 * @returns {UnifiedAppInitializer}
 */
export function getUnifiedApp(config = {}, container = null) {
    if (!globalUnifiedApp) {
        globalUnifiedApp = new UnifiedAppInitializer(config, container);
    }
    return globalUnifiedApp;
}

/**
 * 快速启动统一应用
 * @param {Object} config - 应用配置
 * @param {HTMLElement} container - 容器元素
 * @returns {Promise<UnifiedAppInitializer>}
 */
export async function quickStartUnified(config = {}, container = null) {
    const app = getUnifiedApp(config, container);

    if (!app.isReady()) {
        await app.initialize();
    }

    return app;
}

/**
 * 重置全局应用实例
 * @returns {Promise<void>}
 */
export async function resetGlobalApp() {
    if (globalUnifiedApp) {
        await globalUnifiedApp.destroy();
        globalUnifiedApp = null;
    }
}

/**
 * 获取默认应用实例（兼容性函数）
 * @returns {UnifiedAppInitializer}
 */
export function getDefaultApp() {
    return globalUnifiedApp;
}

/**
 * 设置默认应用实例（兼容性函数）
 * @param {UnifiedAppInitializer} app
 */
export function setDefaultApp(app) {
    globalUnifiedApp = app;
}
// #endregion

// #region 兼容性导出
// 为了保持与现有代码的兼容性，导出一些别名
export { UnifiedAppInitializer as SmartOfficeApp };
export { createUnifiedApp as createSmartOfficeApp };
export { quickStartUnified as quickStart };
// #endregion
