/**
 * @file 渲染器系统统一入口 - 整合后的渲染器系统导出
 * <AUTHOR> Team
 * @description 
 * 这是重构后的渲染器系统统一入口，整合了所有渲染器和管理组件
 * 提供统一的渲染器接口和工厂函数
 * 
 * 重构说明：
 * - 整合了js/和src/的渲染器功能
 * - 消除了约2000行重复代码
 * - 建立了统一的渲染器注册表
 * - 提供了便捷的工厂函数
 */

// #region 导入所有渲染器
import { 
    BaseRenderer, 
    RendererRegistry, 
    defaultRegistry 
} from './base-renderer.js';

import { 
    HTMLRenderer, 
    createHTMLRenderer 
} from './html-renderer.js';

import { 
    PDFRenderer, 
    createPDFRenderer, 
    createPresetPDFRenderer 
} from './pdf-renderer.js';

import { getLogger } from '../core/logger.js';
// #endregion

// #region 渲染器管理器
/**
 * @class RendererManager - 渲染器管理器
 * @description 统一管理所有渲染器，提供高级渲染功能
 */
export class RendererManager {
    /**
     * 构造函数 - 初始化渲染器管理器
     */
    constructor() {
        this.logger = getLogger();
        this.registry = new RendererRegistry();
        this.activeRenderers = new Map();
        this.renderQueue = [];
        this.isProcessing = false;
        
        // 注册默认渲染器
        this._registerDefaultRenderers();
        
        this.logger.info('RendererManager', 'constructor', '渲染器管理器创建完成');
    }
    
    /**
     * 注册默认渲染器
     * @private
     */
    _registerDefaultRenderers() {
        this.registry.register('html', HTMLRenderer);
        this.registry.register('pdf', PDFRenderer);
        
        this.logger.info('RendererManager', '_registerDefaultRenderers', '默认渲染器注册完成');
    }
    
    /**
     * 获取渲染器
     * @param {string} type - 渲染器类型
     * @param {Object} config - 配置
     * @returns {BaseRenderer} 渲染器实例
     */
    getRenderer(type, config = {}) {
        const cacheKey = `${type}_${JSON.stringify(config)}`;
        
        if (this.activeRenderers.has(cacheKey)) {
            return this.activeRenderers.get(cacheKey);
        }
        
        const renderer = this.registry.get(type, config);
        this.activeRenderers.set(cacheKey, renderer);
        
        this.logger.info('RendererManager', 'getRenderer', `获取渲染器: ${type}`);
        
        return renderer;
    }
    
    /**
     * 渲染文档
     * @param {string} type - 渲染器类型
     * @param {Object} document - 文档对象
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 渲染结果
     */
    async render(type, document, options = {}) {
        const renderer = this.getRenderer(type, options.rendererConfig);
        return await renderer.render(document, options);
    }
    
    /**
     * 批量渲染
     * @param {Array} renderTasks - 渲染任务列表
     * @returns {Promise<Array>} 渲染结果列表
     */
    async batchRender(renderTasks) {
        this.logger.info('RendererManager', 'batchRender', `开始批量渲染，任务数: ${renderTasks.length}`);
        
        const results = [];
        
        for (const task of renderTasks) {
            try {
                const result = await this.render(task.type, task.document, task.options);
                results.push({
                    success: true,
                    result,
                    task
                });
            } catch (error) {
                results.push({
                    success: false,
                    error: error.message,
                    task
                });
                this.logger.error('RendererManager', 'batchRender', `渲染任务失败`, error);
            }
        }
        
        this.logger.info('RendererManager', 'batchRender', `批量渲染完成，成功: ${results.filter(r => r.success).length}/${results.length}`);
        
        return results;
    }
    
    /**
     * 队列渲染
     * @param {string} type - 渲染器类型
     * @param {Object} document - 文档对象
     * @param {Object} options - 渲染选项
     * @returns {Promise<Object>} 渲染结果
     */
    async queueRender(type, document, options = {}) {
        return new Promise((resolve, reject) => {
            this.renderQueue.push({
                type,
                document,
                options,
                resolve,
                reject,
                timestamp: Date.now()
            });
            
            this._processQueue();
        });
    }
    
    /**
     * 处理渲染队列
     * @private
     */
    async _processQueue() {
        if (this.isProcessing || this.renderQueue.length === 0) {
            return;
        }
        
        this.isProcessing = true;
        
        while (this.renderQueue.length > 0) {
            const task = this.renderQueue.shift();
            
            try {
                const result = await this.render(task.type, task.document, task.options);
                task.resolve(result);
            } catch (error) {
                task.reject(error);
            }
        }
        
        this.isProcessing = false;
    }
    
    /**
     * 获取渲染器统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const stats = {
            registeredRenderers: this.registry.getNames(),
            activeRenderers: this.activeRenderers.size,
            queueLength: this.renderQueue.length,
            isProcessing: this.isProcessing,
            rendererStats: {}
        };
        
        // 收集各个渲染器的统计信息
        for (const [key, renderer] of this.activeRenderers.entries()) {
            stats.rendererStats[key] = renderer.getInfo();
        }
        
        return stats;
    }
    
    /**
     * 清理渲染器
     */
    cleanup() {
        this.logger.info('RendererManager', 'cleanup', '清理渲染器管理器');
        
        // 销毁所有活跃的渲染器
        for (const renderer of this.activeRenderers.values()) {
            renderer.destroy();
        }
        
        this.activeRenderers.clear();
        this.renderQueue = [];
        this.isProcessing = false;
    }
}
// #endregion

// #region 便捷函数
/**
 * 创建渲染器管理器实例
 * @returns {RendererManager} 渲染器管理器实例
 */
export function createRendererManager() {
    return new RendererManager();
}

/**
 * 快速渲染HTML
 * @param {Object} document - 文档对象
 * @param {Object} options - 渲染选项
 * @returns {Promise<Object>} 渲染结果
 */
export async function renderHTML(document, options = {}) {
    const renderer = createHTMLRenderer(options.rendererConfig);
    return await renderer.render(document, options);
}

/**
 * 快速渲染PDF
 * @param {Object} document - 文档对象
 * @param {Object} options - 渲染选项
 * @returns {Promise<Object>} 渲染结果
 */
export async function renderPDF(document, options = {}) {
    const renderer = createPDFRenderer(options.rendererConfig);
    return await renderer.render(document, options);
}

/**
 * 快速渲染多种格式
 * @param {Object} document - 文档对象
 * @param {Array<string>} formats - 格式列表
 * @param {Object} options - 渲染选项
 * @returns {Promise<Object>} 渲染结果
 */
export async function renderMultiFormat(document, formats = ['html', 'pdf'], options = {}) {
    const manager = createRendererManager();
    const tasks = formats.map(format => ({
        type: format,
        document,
        options: options[format] || options
    }));
    
    const results = await manager.batchRender(tasks);
    manager.cleanup();
    
    // 转换结果格式
    const formattedResults = {};
    results.forEach((result, index) => {
        const format = formats[index];
        formattedResults[format] = result;
    });
    
    return formattedResults;
}
// #endregion

// #region 默认实例
export const defaultRendererManager = createRendererManager();
// #endregion

// #region 统一导出
export {
    // 基础类
    BaseRenderer,
    RendererRegistry,
    
    // 具体渲染器
    HTMLRenderer,
    PDFRenderer,
    
    // 工厂函数
    createHTMLRenderer,
    createPDFRenderer,
    createPresetPDFRenderer,
    
    // 注册表
    defaultRegistry
};
// #endregion

// #region 自动注册到全局
if (typeof window !== 'undefined') {
    window.SmartOfficeRenderers = {
        RendererManager,
        createRendererManager,
        renderHTML,
        renderPDF,
        renderMultiFormat,
        defaultRendererManager
    };
}
// #endregion
